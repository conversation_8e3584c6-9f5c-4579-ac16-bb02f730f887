#pragma once
#include <stdbool.h>
#include <stdint.h>

#include "config.hpp"
#include "can.hpp"

#define JKBMS 1
#define DALYBMS 2

#if POWER_SOURCE == POWER_SOURCE_BATTERY
  #if BATTERY_BMS == JKBMS
  #include "battery_jkbms.hpp"
  #elif BATTERY_BMS == DALYBMS
  #include "battery_dalybms.hpp"
  #else
  #error "Battery BMS Not Defined"
  #endif // BATTERY_BMS
#elif POWER_SOURCE == POWER_SOURCE_INVERTER
#include "inverter_meanwell.hpp"
#else
#error "Power Source Not Defined"
#endif  // POWER_SOURCE

void parse_battery_message(
    const uint32_t can_id,
    CanDevice::RecvDataBuffer_t message,
    float msg_actuation_battery_ros[3]
);
void request_power_information();
