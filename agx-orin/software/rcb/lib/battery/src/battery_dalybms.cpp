#include <cstdint>
#include <cstring>

#include "battery_dalybms.hpp"
#include "utils.hpp"
#include "can.hpp"

void request_battery_information() {
    std::memset(
        CanDevice::getPowerCan()->getSendDataBuffer().begin(),
        0,
        CanDevice::getPowerCan()->getRecvDataBuffer().size()
    );
    for (uint8_t data_id : DALY_BMS_DATA_IDS) {
        CanDevice::getPowerCan()->sendLastPopulatedData(
            DALY_BMS_GENERATE_ID(data_id, ACTUATION_SYSTEM_BATTERY_BMS_ADDRESS),
            0x08,
            true,
            true
        );
    }
}

void parse_battery_soc_data(const uint8_t message[8], const uint8_t bms_address,
                            float msg_actuation_battery_ros[3]) {
  float cumulative_total_voltage =
      CanUtils::interpret_can_bytes_to_decimal_little_endian_daly_bms(
              0.1f, 0.0f, 2,
              message[0],
              message[1]
      ); // 0.1V

  //    float gather_total_voltage =
  //    interpret_can_bytes_to_decimal_little_endian_daly_bms(0.1f, 0.0f, 2,
  //    message[2], message[3]); // 0.1V

  // Negative Sign is to invert current because BMS gives negative value when
  // in discharge and positive when in charge
  float current = -CanUtils::interpret_can_bytes_to_decimal_little_endian_daly_bms(
      0.1f,
      30'000.0f,
      2,
      message[4],
      message[5]
  ); //(30000 Offset, 0.1A)
  float soc = CanUtils::interpret_can_bytes_to_decimal_little_endian_daly_bms(
      0.1f,
      0.0f,
      2,
      message[6],
      message[7]
  ); //(0.1%)
  if (bms_address == 1) {
    msg_actuation_battery_ros[0] = cumulative_total_voltage;
    msg_actuation_battery_ros[1] = current;
    msg_actuation_battery_ros[2] = soc;
  }
}
