#ifndef CONGIG_HPP
#define CONGIG_HPP

// CAN Specific configurations
#define CAN_MSG_DELAY std::chrono::milliseconds(2)

#define AMR_WHEEL_DIAMETER_MM 155.0f
#define AMR_WHEEL_RADIUS_MM (AMR_WHEEL_DIAMETER_MM / 2.0f)
#define AMR_LINEAR_VELOCITY_MpS_TO_ANGULAR_VELOCITY_RPM(v)                     \
  (((v)*60.0) / (2.0 * M_PI * AMR_WHEEL_RADIUS_MM / 1000.0f))

// AMR Travelling Motor Configuration
#define TRAVEL_ENCODER_RESOLUTION 10000.0f
#define TRAVEL_MOTOR_GEAR_RATIO 45.5f
#define AMR_MAX_TRAVEL_SPEED_RPM 2500.0f
const float AMR_TRAVEL_MOTOR_POS_VELOCITY_CONVERSION_FACTOR =
    (512.0f * TRAVEL_ENCODER_RESOLUTION * TRAVEL_MOTOR_GEAR_RATIO) / 1875.0;
const float AMR_TRAVEL_MOTOR_ACC_DEC_CONVERSION_FACTOR =
    (65536.0f * TRAVEL_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;

// AMR Steering Motor Configuration
#define STEERING_ENCODER_RESOLUTION 92500.0f
#define AMR_MAX_STEER_ANGLE 140.0f
#define AMR_STEER_PROFILE_SPEED 250.0f
const float AMR_STEER_MOTOR_POS_VEL_CONVERSION_FACTOR =
    512.0f * STEERING_ENCODER_RESOLUTION / 1875.0;
const float AMR_STEER_MOTOR_ACC_DEC_CONVERSION_FACTOR =
    (65536.0f * STEERING_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;

// LiftKit Motor Configuration
#define LIFTKIT_ENCODER_RESOLUTION 65535.0f
#define LIFTKIT_MIN_HEIGHT_MM 0.0f
#define LIFTKIT_MAX_HEIGHT_MM 1490.0f // 1.4 mtr
const float LIFTKIT_SPEED_RPM = 3000.0f;
const float LIFTKIT_MOTOR_POS_VEL_CONVERSION_FACTOR =
    512.0f * LIFTKIT_ENCODER_RESOLUTION / 1875.0;
const float LIFTKIT_MOTOR_ACC_DEC_CONVERSION_FACTOR =
    (65536.0f * LIFTKIT_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;
const float LIFTKIT_LENGTH_MM_TO_ROTATION_DEG_CONVERSION = 1.0f;
#define LIFTKIT_ENC_PER_MM 32768

// Battery Configuration
#define BATTERY_BMS DALYBMS

// Power Source Configuration
#define POWER_SOURCE_BATTERY 1
#define POWER_SOURCE_INVERTER 2
#define POWER_SOURCE POWER_SOURCE_INVERTER

#define ZeroValue 0x00

// CAN Configuration
#define CAN1_COMMS_RATE (1000 * 1000)
#define CAN2_COMMS_RATE (250 * 1000)
#define CAN_TRANSMIT_BUFFER_SIZE 100U
#define CAN_RECIEVE_BUFFER_SIZE 100U

#endif // CONGIG_HPP
